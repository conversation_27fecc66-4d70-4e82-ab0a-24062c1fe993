import 'dart:convert';

import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/core/shared_widgets/base_mobile_view_widget/base_mobile_view.widget.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/content_details.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class CreateQuestionScreen extends HookConsumerWidget {
  final ContentModel content;

  const CreateQuestionScreen({
    super.key,
    required this.content,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkService = ref.watch(networkServiceProvider);
    final questionController = useTextEditingController();
    final answerController = useTextEditingController();
    final isLoading = useState(false);
    final formKey = useMemoized(() => GlobalKey<FormState>());

    Future<void> createQuestion() async {
      if (!formKey.currentState!.validate()) return;

      try {
        isLoading.value = true;

        await networkService.postResponse(
          ApiEndPoints.createQuestion,
          body: {
            'content_id': content.id,
            'question': questionController.text.trim(),
            'answer': answerController.text.trim(),
          },
        );

        // Navigate back to content details with questions tab selected
        final questionsTabIndex =
            ContentDetailsScreen.getQuestionsTabIndex(content);
        context.back();
        context.toReplacement(ContentDetailsScreen(
          mainContent: content,
          initialTabIndex: questionsTabIndex,
        ));
        context.showBarMessage('تم إنشاء السؤال بنجاح');
      } catch (e) {
        context.showBarMessage(jsonDecode(e.toString())['message'].toString(),
            isError: true);
      } finally {
        isLoading.value = false;
      }
    }

    return MobileDesignWidget(
      child: Scaffold(
        backgroundColor: ColorManager.backgroundColor,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          title: Text(
            'إنشاء سؤال جديد',
            style: AppTextStyles.title,
          ),
          leading: IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back_ios),
          ),
        ),
        body: Form(
          key: formKey,
          child: ListView(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            children: [
              // Question Field
              Text(
                'السؤال *',
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.smallGap,
              TextFormField(
                controller: questionController,
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'اكتب السؤال هنا...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    borderSide:
                        const BorderSide(color: ColorManager.primaryColor),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال السؤال';
                  }
                  if (value.trim().length < 10) {
                    return 'يجب أن يكون السؤال أكثر من 10 أحرف';
                  }
                  return null;
                },
              ),

              AppGaps.largeGap,

              // Answer Field (Optional)
              Text(
                'الإجابة (اختيارية)',
                style: AppTextStyles.subTitle.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              AppGaps.smallGap,
              TextFormField(
                controller: answerController,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'اكتب الإجابة هنا (اختيارية)...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                    borderSide:
                        const BorderSide(color: ColorManager.primaryColor),
                  ),
                ),
              ),

              AppGaps.xLargeGap,

              // Create Button
              SizedBox(
                height: 50,
                child: ElevatedButton(
                  onPressed: isLoading.value ? null : createQuestion,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorManager.primaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(AppRadius.smallRadius),
                    ),
                  ),
                  child: isLoading.value
                      ? const Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 10),
                            Text('جاري الإنشاء...'),
                          ],
                        )
                      : Text(
                          'إنشاء السؤال',
                          style: AppTextStyles.subTitle.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),

              AppGaps.mediumGap,

              // Info Text
              Container(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                decoration: BoxDecoration(
                  color: ColorManager.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(AppRadius.smallRadius),
                  border: Border.all(
                    color: ColorManager.primaryColor.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.lightbulb_outline,
                          color: ColorManager.primaryColor,
                          size: 20,
                        ),
                        AppGaps.smallGap,
                        Text(
                          'نصائح:',
                          style: AppTextStyles.labelLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    AppGaps.smallGap,
                    Text(
                      '• اكتب سؤالاً واضحاً ومفهوماً\n'
                      '• يمكنك ترك الإجابة فارغة وإضافتها لاحقاً\n'
                      '• يمكن للذكاء الاصطناعي توليد إجابة تلقائياً',
                      style: AppTextStyles.labelMedium.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
