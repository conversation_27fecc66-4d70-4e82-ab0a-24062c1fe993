import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/core/providers/network_api_service_provider.dart';
import 'package:dawraq/src/core/shared_widgets/loading/loading_widget.dart';
import 'package:dawraq/src/core/theme/color_manager.dart';
import 'package:dawraq/src/core/theme/font_styles.dart';
import 'package:dawraq/src/features/auth/models/user_model_helper.dart';
import 'package:dawraq/src/features/content/models/content_model.dart';
import 'package:dawraq/src/features/content/ui/content_details_screen/content_details.screen.dart';
import 'package:dawraq/src/features/content/ui/create_question_screen/create_question.screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class QuestionsAnswersWidget extends HookConsumerWidget {
  final List<QuestionAnswer> questionsAnswers;
  final Color color;
  final ContentModel content;

  const QuestionsAnswersWidget({
    super.key,
    required this.questionsAnswers,
    required this.color,
    required this.content,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final networkService = ref.watch(networkServiceProvider);
    final loadingStates = useState<Map<int, bool>>({});

    void navigateBackToQuestionsTab() {
      final questionsTabIndex =
          ContentDetailsScreen.getQuestionsTabIndex(content);
      context.toReplacement(ContentDetailsScreen(
        mainContent: content,
        initialTabIndex: questionsTabIndex,
      ));
    }

    Future<void> navigateToCreateQuestion() async {
      final result = await Navigator.push<bool>(
        context,
        MaterialPageRoute(
          builder: (context) => CreateQuestionScreen(content: content),
        ),
      );

      if (result == true) {
        // Navigate back to content details with questions tab selected
        navigateBackToQuestionsTab();
      }
    }

    Future<void> generateAiAnswer(int questionId) async {
      try {
        loadingStates.value = {...loadingStates.value, questionId: true};

        await networkService.postResponse(
          '${ApiEndPoints.addAiAnswer}/$questionId',
          body: {},
          // body: {'question_id': questionId},
        );

        // Navigate back to refresh the questions tab
        navigateBackToQuestionsTab();
        // Show success message
        context.showBarMessage('تم توليد الإجابة بنجاح');
      } catch (e) {
        context.showBarMessage('خطأ في توليد الإجابة', isError: true);
      } finally {
        loadingStates.value = {...loadingStates.value, questionId: false};
      }
    }

    Future<void> updateAnswer(int questionId, String currentAnswer) async {
      final answerController = TextEditingController(text: currentAnswer);

      final result = await showDialog<String>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('تعديل الإجابة'),
          content: TextField(
            controller: answerController,
            maxLines: 5,
            decoration: const InputDecoration(
              hintText: 'اكتب الإجابة هنا...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, answerController.text),
              child: const Text(
                'حفظ',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      );

      if (result != null && result.isNotEmpty) {
        try {
          loadingStates.value = {...loadingStates.value, questionId: true};

          await networkService.postResponse(
            ApiEndPoints.updateQuestion,
            body: {
              'question_id': questionId,
              'answer': result,
            },
          );

          // Navigate back to refresh the questions tab
          navigateBackToQuestionsTab();
          context.showBarMessage('تم حفظ الإجابة بنجاح');
        } catch (e) {
          context.showBarMessage('خطأ في حفظ الإجابة', isError: true);
        } finally {
          loadingStates.value = {...loadingStates.value, questionId: false};
        }
      }
    }

    Widget buildContent() {
      if (questionsAnswers.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.quiz_outlined,
                size: 64,
                color: ColorManager.darkGrey.withOpacity(0.5),
              ),
              AppGaps.mediumGap,
              Text(
                'لا توجد أسئلة متاحة',
                style: AppTextStyles.title.copyWith(
                  color: ColorManager.darkGrey,
                ),
              ),
              AppGaps.smallGap,
              if (UserModelHelper.canAddQuestions())
                Text(
                  'اضغط على زر + لإضافة سؤال جديد',
                  style: AppTextStyles.subTitle.copyWith(
                    color: ColorManager.darkGrey,
                  ),
                ),
            ],
          ),
        );
      }

      return ListView(
        padding: const EdgeInsets.all(AppSpaces.mediumPadding),
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 5,
                backgroundColor: color,
              ),
              AppGaps.smallGap,
              Text(
                'الأسئلة والأجوبة',
                style: AppTextStyles.title,
              ),
            ],
          ),
          AppGaps.mediumGap,
          ...questionsAnswers.map((qa) => _buildQuestionCard(
                context,
                qa,
                loadingStates.value[qa.id] ?? false,
                generateAiAnswer,
                updateAnswer,
              )),
        ],
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: buildContent(),
      floatingActionButton: UserModelHelper.canAddQuestions()
          ? FloatingActionButton(
              onPressed: navigateToCreateQuestion,
              backgroundColor: ColorManager.primaryColor,
              foregroundColor: Colors.white,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildQuestionCard(
    BuildContext context,
    QuestionAnswer qa,
    bool isLoading,
    Function(int) onGenerateAiAnswer,
    Function(int, String) onUpdateAnswer,
  ) {
    final isMyQuestion = UserModelHelper.isMyData(qa.userId);

    return Card(
      margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Text(
            qa.question,
            style: AppTextStyles.subTitle.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppGaps.smallGap,
              Row(
                children: [
                  _buildInfoChip('الصعوبة: ${qa.difficultyLabel}',
                      ColorManager.primaryColor),
                  AppGaps.smallGap,
                  _buildInfoChip(
                      qa.isAiGeneratedLabel,
                      qa.isAiGenerated
                          ? ColorManager.successColor
                          : ColorManager.darkGrey),
                ],
              ),
              if (qa.createdAtHuman != null) ...[
                AppGaps.xSmallGap,
                Text(
                  'تاريخ الإنشاء: ${qa.createdAtHuman!.date}',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: ColorManager.darkGrey,
                  ),
                ),
              ],
            ],
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // AI Answer button
              if (UserModelHelper.canGenerateAnswers() && qa.answer.isEmpty)
                IconButton(
                  onPressed:
                      isLoading ? null : () => onGenerateAiAnswer(qa.id!),
                  icon: isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.auto_awesome,
                          color: ColorManager.primaryColor),
                  tooltip: 'توليد إجابة بالذكاء الاصطناعي',
                ),
              // Edit button for own questions
              if (isMyQuestion && UserModelHelper.canEditQuestions())
                IconButton(
                  onPressed: isLoading
                      ? null
                      : () => onUpdateAnswer(qa.id!, qa.answer),
                  icon: const Icon(Icons.edit, color: ColorManager.darkGrey),
                  tooltip: 'تعديل الإجابة',
                ),
              const Icon(Icons.expand_more),
            ],
          ),
          children: [
            if (qa.answer.isNotEmpty) ...[
              Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.question_answer,
                            color: ColorManager.successColor, size: 20),
                        AppGaps.smallGap,
                        Text(
                          'الإجابة:',
                          style: AppTextStyles.subTitle.copyWith(
                            fontWeight: FontWeight.bold,
                            color: ColorManager.successColor,
                          ),
                        ),
                      ],
                    ),
                    AppGaps.mediumGap,
                    HtmlWidget(
                      qa.answer,
                      onLoadingBuilder: (context, element, loadingProgress) =>
                          const Center(child: LoadingWidget()),
                    ),
                  ],
                ),
              ),
            ] else ...[
              Padding(
                padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                child: Text(
                  'لا توجد إجابة متاحة',
                  style: AppTextStyles.labelSmall.copyWith(
                    color: ColorManager.darkGrey,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoChip(String label, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppSpaces.smallPadding,
        vertical: AppSpaces.xSmallPadding,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppRadius.smallRadius),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        label,
        style: AppTextStyles.labelSmall.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
