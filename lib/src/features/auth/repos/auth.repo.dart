import 'package:dawraq/src/core/data/remote/response/api_end_points.dart';
import 'package:dawraq/src/features/auth/models/user_model.dart';
import 'package:xr_helper/xr_helper.dart';

class AuthRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  AuthRepo(this._networkApiService);

  // * Login ================================
  Future<UserModel> login({required UserModel user}) async {
    return baseFunction(
      () async {
        final response = await _networkApiService.postResponse(
          ApiEndPoints.login,
          body: user.toJson(),
        );

        final responseData = response['data'];

        if (responseData == null) {
          throw Exception(response['message']);
        }

        final loggedUser = UserModel.fromJson(responseData['user']);

        Log.f('LoggedUSER: $loggedUser');

        //! Save User to local
        _saveUserData(responseData);

        return loggedUser;
      },
    );
  }

  // * Send Contact Message ================================
  Future<void> sendContactMessage({
    required Map<String, dynamic> data,
    required List<String> filesPaths,
  }) async {
    return baseFunction(
      () async {
        await _networkApiService.postResponse(
          ApiEndPoints.sendMessage,
          body: data,
          filePaths: filesPaths,
          fieldName: 'attachments[]',
        );
      },
    );
  }

  // * Save User Data ================================
  void _saveUserData(Map<String, dynamic> responseData) {
    GetStorageService.setLocalData(
      key: LocalKeys.token,
      value: responseData['token'],
    );

    GetStorageService.setLocalData(
      key: LocalKeys.user,
      value: responseData['user'],
    );
  }

  // * Logout ================================
  Future<void> logout() async {
    return baseFunction(
      () async {
        GetStorageService.clearLocalData();
      },
    );
  }
}
