import 'dart:async';

import 'package:file_picker/file_picker.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_media_repo.dart';

final mediaPickerControllerProvider =
    ChangeNotifierProvider<MediaPickerController>(
  (ref) {
    final mediaLocalRepoProvider = ref.watch(localMediaRepoProvider);

    return MediaPickerController(
      localMediaRepo: mediaLocalRepoProvider,
    );
  },
);

class MediaPickerController extends BaseController {
  final LocalMediaRepo localMediaRepo;

  MediaPickerController({
    required this.localMediaRepo,
  });

  FilePickerResult? _filePickerResult;

  List<String> get filesPaths =>
      _filePickerResult?.paths.map((e) => e ?? '').toList() ?? [];

  String get filePath => _filePickerResult?.files.firstOrNull?.path ?? '';

  Future<FilePickerResult?> pickFile({
    bool imageUpload = false,
    bool allowMultiple = true,
    bool useCamera = false,
  }) async {
    try {
      final oldFiles = _filePickerResult?.files ?? [];

      final pickedFiles = await localMediaRepo.pickFiles(
        imageUpload: imageUpload,
        uploadMultiple: allowMultiple,
        useCamera: useCamera,
      );

      if (pickedFiles == null) return null;

      if (oldFiles.isNotEmpty) {
        pickedFiles.files.insertAll(0, oldFiles);
      }

      _filePickerResult = pickedFiles;

      notifyListeners();

      return pickedFiles;
    } on Exception catch (e) {
      Log.e('Error Getting File $e');
      rethrow;
    }
  }

  void clearFiles() {
    _filePickerResult = null;

    Log.i('FileClearedSuccessfully');

    notifyListeners();
  }

  void removeFile(int index) {
    _filePickerResult?.files.removeAt(index);
    notifyListeners();
  }
}
