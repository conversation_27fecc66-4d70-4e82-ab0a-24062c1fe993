part of xr_helper;

abstract class BaseApiServices with XNetworkHelper {
  Future<dynamic> getResponse(String url);

  Future<dynamic> postResponse(String url,
      {
        required Map<String, dynamic> body,
        List<String> filePaths = const [],
        String? fieldName,
      });

  Future<dynamic> putResponse(String url, {
    required Map<String, dynamic> data,
    List<String> filePaths = const [],
    String? fieldName,
  });

  Future<dynamic> deleteResponse(String url);
}
